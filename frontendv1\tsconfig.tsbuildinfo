{"root": ["./src/app.tsx", "./src/main.tsx", "./src/vite-env.d.ts", "./src/components/approval/taskapprovalhistory.tsx", "./src/components/approval/taskapprovallist.tsx", "./src/components/auth/authlayout.tsx", "./src/components/auth/protectedroute.tsx", "./src/components/auth/roleprotectedcomponent.tsx", "./src/components/auth/roleprotectedroute.tsx", "./src/components/common/audithistory.tsx", "./src/components/common/audittrail.tsx", "./src/components/common/graphqlerrorhandler.tsx", "./src/components/common/loadingspinner.tsx", "./src/components/dashboard/kpicard.tsx", "./src/components/dashboard/sitecard.tsx", "./src/components/data/datadashboard.tsx", "./src/components/data/formtemplates.tsx", "./src/components/data/incidenttypes.tsx", "./src/components/data/ppecatalog.tsx", "./src/components/data/permittypes.tsx", "./src/components/data/tradesskills.tsx", "./src/components/data/trainingprograms.tsx", "./src/components/data/shared/quickactioncard.tsx", "./src/components/data/shared/statcard.tsx", "./src/components/data/shared/tabcontainer.tsx", "./src/components/documents/compliancetracker.tsx", "./src/components/documents/documentcategories.tsx", "./src/components/documents/documentlibrary.tsx", "./src/components/documents/documentreports.tsx", "./src/components/documents/documentsearch.tsx", "./src/components/documents/documentupload.tsx", "./src/components/documents/documentviewer.tsx", "./src/components/documents/documentsdashboard.tsx", "./src/components/documents/sitecompliancetracker.tsx", "./src/components/documents/sitedocumentreports.tsx", "./src/components/documents/hooks/usedocuments.ts", "./src/components/documents/shared/documentcard.tsx", "./src/components/equipment/equipmentanalytics.tsx", "./src/components/equipment/equipmentdashboard.tsx", "./src/components/equipment/equipmentinspections.tsx", "./src/components/equipment/equipmentmaintenance.tsx", "./src/components/equipment/generalequipment.tsx", "./src/components/equipment/ppeinventory.tsx", "./src/components/forms/dynamicform.tsx", "./src/components/forms/formfield.tsx", "./src/components/inspections/activeinspections.tsx", "./src/components/inspections/inspectionhistory.tsx", "./src/components/inspections/inspectionsdashboard.tsx", "./src/components/inspections/myinspections.tsx", "./src/components/inspections/scheduledinspections.tsx", "./src/components/inspections/shared/metriccard.tsx", "./src/components/inspections/shared/quickactioncard.tsx", "./src/components/inspections/shared/recentinspections.tsx", "./src/components/inspections/shared/upcominginspections.tsx", "./src/components/layout/floatingcard.tsx", "./src/components/layout/helpbutton.tsx", "./src/components/layout/sidebar.tsx", "./src/components/layout/topbar.tsx", "./src/components/layout/sidebar/flyoutmenuitem.tsx", "./src/components/layout/sidebar/sidebar.tsx", "./src/components/layout/sidebar/sidebarbackbutton.tsx", "./src/components/layout/sidebar/sidebarflyout.tsx", "./src/components/layout/sidebar/sidebarlogo.tsx", "./src/components/layout/sidebar/sidebarmenu.tsx", "./src/components/layout/sidebar/sidebarmenuitem.tsx", "./src/components/layout/sidebar/sidebarprovider.tsx", "./src/components/layout/sidebar/sitedetailsflyout.tsx", "./src/components/layout/sidebar/siteflyoutmenuitem.tsx", "./src/components/layout/sidebar/sitehoverdetails.tsx", "./src/components/layout/sidebar/index.ts", "./src/components/layout/sidebar/__tests__/sidebar.test.tsx", "./src/components/nextday/disapprovedtasks.tsx", "./src/components/nextday/taskscheduling.tsx", "./src/components/notifications/notificationsystem.tsx", "./src/components/payroll/sitepayrolltab.tsx", "./src/components/permits/activepermits.tsx", "./src/components/permits/allpermits.tsx", "./src/components/permits/permitcreationmodal.tsx", "./src/components/permits/permithistory.tsx", "./src/components/permits/permitreports.tsx", "./src/components/permits/permitsdashboard.tsx", "./src/components/permits/shared/permitprioritybadge.tsx", "./src/components/permits/shared/permitstatusbadge.tsx", "./src/components/ppe/ppedashboard.tsx", "./src/components/ppe/ppemaintenance.tsx", "./src/components/ppe/ppeworkerrelation.tsx", "./src/components/reports/crosssiteperformance.tsx", "./src/components/reports/incidentstatistics.tsx", "./src/components/reports/trainingcompliance.tsx", "./src/components/safety/incidentmanagement.tsx", "./src/components/safety/observationassignmentmodal.tsx", "./src/components/safety/observationform.tsx", "./src/components/safety/observationreviewmodal.tsx", "./src/components/safety/safetydashboard.tsx", "./src/components/safety/safetyobservations.tsx", "./src/components/safety/siterams.tsx", "./src/components/safety/standaloneobservationform.tsx", "./src/components/safety/toolboxtalks.tsx", "./src/components/safety/hooks/usesafetydata.ts", "./src/components/safety/hooks/usesafetyfilters.ts", "./src/components/safety/shared/safetykpicard.tsx", "./src/components/safety/shared/safetytable.tsx", "./src/components/safety/types/safety.ts", "./src/components/settings/companyprofile.tsx", "./src/components/settings/compliancesettings.tsx", "./src/components/settings/integrationsettings.tsx", "./src/components/settings/notificationsettings.tsx", "./src/components/settings/rolespermissions.tsx", "./src/components/settings/settingsoverview.tsx", "./src/components/settings/systemconfig.tsx", "./src/components/settings/usermanagement.tsx", "./src/components/tasks/activetaskslist.tsx", "./src/components/tasks/taskcreationmodal.tsx", "./src/components/tasks/taskexplorer.tsx", "./src/components/tasks/taskhistorylist.tsx", "./src/components/tasks/taskrequestworkflow.tsx", "./src/components/tasks/taskrequestslist.tsx", "./src/components/tasks/tasktabs.tsx", "./src/components/tasks/tasktemplatesview.tsx", "./src/components/tasks/tasksdashboard.tsx", "./src/components/tasks/taskskanban.tsx", "./src/components/tasks/taskslist.tsx", "./src/components/tasks/tasksreports.tsx", "./src/components/tasks/request/approvalactions.tsx", "./src/components/tasks/request/controlmeasuremanagement.tsx", "./src/components/tasks/request/documentmanagement.tsx", "./src/components/tasks/request/hazardmanagement.tsx", "./src/components/tasks/request/taskrequestoverview.tsx", "./src/components/tasks/shared/taskprioritybadge.tsx", "./src/components/tasks/shared/taskstatusbadge.tsx", "./src/components/tasks/tabs/audittrailtab.tsx", "./src/components/tasks/tabs/controlmeasurestab.tsx", "./src/components/tasks/tabs/documenttab.tsx", "./src/components/tasks/tabs/hazardstab.tsx", "./src/components/tasks/tabs/linkeditemstab.tsx", "./src/components/tasks/tabs/locationtab.tsx", "./src/components/tasks/tabs/progresstab.tsx", "./src/components/tasks/tabs/requestertab.tsx", "./src/components/tasks/tabs/riskassessmenttab.tsx", "./src/components/tasks/tabs/scheduletab.tsx", "./src/components/tasks/tabs/taskdetailstab.tsx", "./src/components/time/attendancetab.tsx", "./src/components/time/edittimelogmodal.tsx", "./src/components/time/faceregistrationmodal.tsx", "./src/components/time/overtimetab.tsx", "./src/components/time/reportstab.tsx", "./src/components/time/syncattendancemodal.tsx", "./src/components/time/systemtab.tsx", "./src/components/time/terminalconfigmodal.tsx", "./src/components/time/terminalstatuspanel.tsx", "./src/components/time/timelogfilters.tsx", "./src/components/time/timelogtable.tsx", "./src/components/toolbox/toolboxattendance.tsx", "./src/components/toolbox/toolboxdashboard.tsx", "./src/components/toolbox/toolboxinductions.tsx", "./src/components/toolbox/toolboxreports.tsx", "./src/components/toolbox/toolboxsessionform.tsx", "./src/components/toolbox/toolboxsessionlist.tsx", "./src/components/toolbox/toolboxtasks.tsx", "./src/components/training/audittrailvisualization.tsx", "./src/components/training/bulktrainingassignment.tsx", "./src/components/training/newtrainingform.tsx", "./src/components/training/taskeligibilitygating.tsx", "./src/components/training/trainingassignmentmodal.tsx", "./src/components/training/trainingcalendar.tsx", "./src/components/training/trainingcompletion.tsx", "./src/components/training/trainingcompletionmodal.tsx", "./src/components/training/trainingdashboard.tsx", "./src/components/training/trainingexpirationalerts.tsx", "./src/components/training/trainingexpirytracker.tsx", "./src/components/training/trainingform.tsx", "./src/components/training/trainingmatrix.tsx", "./src/components/training/trainingprogrammaster.tsx", "./src/components/training/trainingreports.tsx", "./src/components/training/trainingschedulemodal.tsx", "./src/components/training/workertrainingstatus.tsx", "./src/components/training/shared/compliancestatusbadge.tsx", "./src/components/training/shared/quickactioncard.tsx", "./src/components/training/shared/trainingmodal.tsx", "./src/components/training/shared/trainingstatuscard.tsx", "./src/components/weather/loggedweatherdata.tsx", "./src/components/weather/siteweathercard.tsx", "./src/components/weather/weatherdashboard.tsx", "./src/components/weather/weatherdropdown.tsx", "./src/components/weather/weathericon.tsx", "./src/components/weather/weatherindicator.tsx", "./src/components/worker/payrolltab.tsx", "./src/components/workers/createworkerdemo.tsx", "./src/components/workers/createworkerform.tsx", "./src/components/workers/photoupload.tsx", "./src/components/workers/workerformintegrationexample.tsx", "./src/data/inspectionformtemplate.ts", "./src/data/mockdata.ts", "./src/data/mockforms.ts", "./src/data/mocktenantdata.ts", "./src/graphql/mutations.ts", "./src/graphql/queries.ts", "./src/hooks/useauthcontext.tsx", "./src/hooks/usegraphql.ts", "./src/hooks/usehashnavigation.ts", "./src/hooks/usesitecontext.ts", "./src/hooks/usetenantcontext.tsx", "./src/hooks/useweather.ts", "./src/hooks/usercontext.tsx", "./src/mock/sitedata.ts", "./src/mock/taskdata.ts", "./src/pages/accountpage.tsx", "./src/pages/companyreports.tsx", "./src/pages/confinedspaceformdemopage.tsx", "./src/pages/confinedspaceformpage.tsx", "./src/pages/createworkerpage.tsx", "./src/pages/dashboard.tsx", "./src/pages/datapage.tsx", "./src/pages/documentspage.tsx", "./src/pages/equipmentpage.tsx", "./src/pages/excavationformdemopage.tsx", "./src/pages/excavationformpage.tsx", "./src/pages/forgotpasswordpage.tsx", "./src/pages/formspage.tsx", "./src/pages/hotworkformdemopage.tsx", "./src/pages/hotworkformpage.tsx", "./src/pages/inspectionformdemopage.tsx", "./src/pages/inspectionformpage.tsx", "./src/pages/inspectionformslistpage.tsx", "./src/pages/inspectionspage.tsx", "./src/pages/loginpage.tsx", "./src/pages/nextdaytaskspage.tsx", "./src/pages/notificationspage.tsx", "./src/pages/ppepage.tsx", "./src/pages/ptwformdemopage.tsx", "./src/pages/ptwformpage.tsx", "./src/pages/pagenotfound.tsx", "./src/pages/permitspage.tsx", "./src/pages/registerpage.tsx", "./src/pages/resetpasswordpage.tsx", "./src/pages/safetypage.tsx", "./src/pages/settings.tsx", "./src/pages/sitedashboard.tsx", "./src/pages/sitedocumentspage.tsx", "./src/pages/siteinfopage.tsx", "./src/pages/standaloneobservationpage.tsx", "./src/pages/taskapprovalpage.tsx", "./src/pages/taskcreatepage.tsx", "./src/pages/taskdetailpage.tsx", "./src/pages/taskrequestpage.tsx", "./src/pages/taskspage.tsx", "./src/pages/timemanagement.tsx", "./src/pages/toolboxpage.tsx", "./src/pages/trainingpage.tsx", "./src/pages/weatherpage.tsx", "./src/pages/workatheightformdemopage.tsx", "./src/pages/workatheightformpage.tsx", "./src/pages/workerdirectory.tsx", "./src/pages/workerformdemopage.tsx", "./src/pages/workerprofile.tsx", "./src/services/authservice.ts", "./src/services/mockgraphqlclient.ts", "./src/services/mocktenantgraphqlclient.ts", "./src/services/taskservice.ts", "./src/services/weatherservice.ts", "./src/styles/sidebar-tokens.ts", "./src/types/auth.ts", "./src/types/documents.ts", "./src/types/equipment.ts", "./src/types/forms.ts", "./src/types/index.ts", "./src/types/masterdata.ts", "./src/types/permits.ts", "./src/types/sidebar.ts", "./src/types/tasktemplates.ts", "./src/types/tasks.ts", "./src/types/tenant-equipment.ts", "./src/types/time.ts", "./src/types/training.ts", "./src/types/weather.ts", "./src/utils/excavationptw.ts", "./src/utils/generalptw.ts", "./src/utils/workingheightptw.ts", "./src/utils/confinedspaceentrptw.ts", "./src/utils/hotworksptw.ts", "./src/utils/routeutils.ts", "./src/utils/timeutils.ts"], "version": "5.8.3"}