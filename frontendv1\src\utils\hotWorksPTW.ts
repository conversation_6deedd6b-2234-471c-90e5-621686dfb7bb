const hotWorkPermitTitle = "Hot Work Permit";
const hotWorkPermitDescription = "This permit is valid for one day only.";

const hotWorkPermitData = [
    {
        name: "Can the hotwork be avoided?",
        value: "Yes/No",
        type: "radio",
        lastDetails: "If NO proceed to raise the Permit To Work."
    },
    {
        "Details": [
            {
                name: "PTW Ref.No",
                type: "text",
                required: true
            },
            {
                name: "Starting from",
                type: "datetime",
                required: true
            },
            {
                name: "Ending at",
                type: "datetime",
                required: true
            },
        ]
    },
    {
        "Other permits in use": [
            {
                name: "GWP",
                type: "checkbox",
                required: false
            },
            {
                name: "HWP",
                type: "checkbox",
                required: false
            },
            {
                name: "Electrical permit",
                type: "checkbox",
                required: false
            },
            {
                name: "CSE",
                type: "checkbox",
                required: false
            },
        ]
    },
    {
        "Nature of work": [
            {
                name: "welding",
                type: "checkbox",
                required: false
            },
            {
                name: "brazing",
                type: "checkbox",
                required: false
            },
            {
                name: "cutting",
                type: "checkbox",
                required: false
            },
            {
                name: "grinding",
                type: "checkbox",
                required: false
            },
            {
                name: "gas cutting",
                type: "checkbox",
                required: false
            },
        ]
    },
    {
        "Description of work": [
            {
                name: "Description of work",
                type: "text",
                required: true
            },
            {
                name: "Location",
                type: "text",
                required: true
            },
        ]
    },
    {
        "Documents to be attached": [
            {
                name: "Method statement",
                type: "checkbox",
                required: false
            },
            {
                name: "Risk assessment",
                type: "checkbox",
                required: false
            },
            {
                name: "Emergency rescue plan",
                type: "checkbox",
                required: false
            },
            {
                name: "SSWP",
                type: "checkbox",
                required: false
            },
            {
                name: "Other(Specify)",
                type: "text",
                required: false
            },
        ]
    },
    {
        "hazards": [
            {
                name: "fire",
                type: "checkbox",
                required: false
            },
            {
                name: "welding arc",
                type: "checkbox",
                required: false
            },
            {
                name: "grinding sparks",
                type: "checkbox",
                required: false
            },
            {
                name: "rotating parts",
                type: "checkbox",
                required: false
            },

            {
                name: "weather",
                type: "checkbox",
                required: false
            },
            {
                name: "noise",
                type: "checkbox",
                required: false
            },
            {
                name: "flying particles",
                type: "checkbox",
                required: false
            },
            {
                name: "bursting of the discs",
                type: "checkbox",
                required: false
            },

            {
                name: "vibrations",
                type: "checkbox",
                required: false
            },
            {
                name: "lone working",
                type: "checkbox",
                required: false
            },
            {
                name: "electricity",
                type: "checkbox",
                required: false
            },
            {
                name: "exposed cables",
                type: "checkbox",
                required: false
            },
            {
                name: "other(Specify)",
                type: "text",
                required: false
            },

        ]
    },
    {
        "precautions required": [
            {
                name: "Institution of a welding",
                type: "checkbox",
                required: false
            },
            {
                name: "exclusion of the work area using barriers",
                type: "checkbox",
                required: false
            },
            {
                name: "post safety signages",
                type: "checkbox",
                required: false
            },
            {
                name: "fire extinguisher",
                type: "checkbox",
                required: false
            },

            {
                name: "competent welder",
                type: "checkbox",
                required: false
            },
            {
                name: "close supervision",
                type: "checkbox",
                required: false
            },
            {
                name: "inspection of the work area",
                type: "checkbox",
                required: false
            },
            {
                name: "Sparks directed towards the canvas material.",
                type: "checkbox",
                required: false
            },
            {
                name: "Institution of the guards on the angle grinders.",
                type: "checkbox",
                required: false
            },
            {
                name: "No loose clothing",
                type: "checkbox",
                required: false
            },
            {
                name: "other(Specify)",
                type: "text",
                required: false
            },

        ]
    },
    {
        "PPE": [
            {
                name: "leather gloves",
                type: "checkbox",
                required: false
            },
            {
                name: "face visor",
                type: "checkbox",
                required: false
            },
            {
                name: "welding goggles",
                type: "checkbox",
                required: false
            },

            {
                name: "ear muffs",
                type: "checkbox",
                required: false
            },
            {
                name: "ear plugs",
                type: "checkbox",
                required: false
            },
            {
                name: "overalls",
                type: "checkbox",
                required: false
            },


            {
                name: "welding visor",
                type: "checkbox",
                required: false
            },
            {
                name: "leather aprons",
                type: "checkbox",
                required: false
            },
            {
                name: "clear impact resistant glasses/goggles",
                type: "checkbox",
                required: false
            },

        ]
    },
    {
        "Fire extinguisher": [
            {
                name: "dry powder",
                type: "checkbox",
                required: false
            },
            {
                name: "foam",
                type: "checkbox",
                required: false
            },
            {
                name: "co2",
                type: "checkbox",
                required: false
            },
        ]
    },
    {
        "Fire watch (Before commencement of works)": {
            description: "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediately notify the issuer of any changes to the conditions governing this permit to work.",
            items: [
                {
                    name: "Name",
                    type: "text",
                    required: true
                },
                {
                    name: "Signature",
                    type: "signature",
                    required: true
                },
                {
                    name: "Date",
                    type: "date",
                    required: true
                },
                {
                    name: "Time",
                    type: "time",
                    required: true
                },
            ]
        }
    },
    {
        "Permit Issue": {
            "description": "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediatley notify the issuer of any changes to the conditions governing this permit to work.",
            "items": [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]

        }
    },
    {
        "Fire Marshal (After work)": {
            description: "I have read this permit and confirm that the location where the work outlined is being carried out has been personally examined and the precautions have been checked as indicated. The permit requirements shall be communicated to all the persons involved in the work/task and I will immediately notify the issuer of any changes to the conditions governing this permit to work.",
            items: [
                {
                    name: "Name",
                    type: "text",
                    required: true
                },
                {
                    name: "Signature",
                    type: "signature",
                    required: true
                },
                {
                    name: "Date",
                    type: "date",
                    required: true
                },
                {
                    name: "Time",
                    type: "time",
                    required: true
                },
            ]
        }
    },
    {
        "Permit Return": {
            "description": "I accept that this work has been completed satisfactorily, the personnel and the equipment have been inspected and tagged as appropriate. The work area has been properly housekept.",
            "items": [
                {
                    "Competent Person (Permit Receiver)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
                {
                    "Authorizing Person (Permit Issuer)": [
                        {
                            name: "Name",
                            type: "text",
                            required: true
                        },
                        {
                            name: "Signature",
                            type: "signature",
                            required: true
                        },
                        {
                            name: "Date",
                            type: "date",
                            required: true
                        },
                        {
                            name: "Time",
                            type: "time",
                            required: true
                        },
                    ]
                },
            ]

        }
    },
    {
        title: "Sign off",
        type: "signoff",
        description: "By signing this hot work permit, I accept to abide by the instituted control measures that will enhance safe hot work operations",
        items: [
            {
                name: "Fire safety supervisor",
                type: "text",
                required: true
            },
            {
                name: "Supervisor Signature",
                type: "signature",
                required: true
            }
        ],
        tableHeader: [
            {
                name: "Name",
                type: "text"
            },
            {
                name: "Designation",
                type: "text"
            },
            {
                name: "Signature",
                type: "signature"
            },
            {
                name: "Time",
                type: "time"
            }
        ]
    }
]

export { hotWorkPermitData, hotWorkPermitTitle, hotWorkPermitDescription };